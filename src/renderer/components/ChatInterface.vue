<template>
  <div class="chat-interface">
    <!-- 空状态 -->
    <div v-if="!currentTheme" class="empty-state">
      <div class="empty-content">
        <NIcon size="64" color="#d0d0d0">
          <ChatbubbleEllipsesOutline />
        </NIcon>
        <h3>选择一个主题开始聊天</h3>
        <p>在左侧选择一个主题，或创建一个新的主题来开始记录笔记</p>
      </div>
    </div>

    <!-- 有主题时的分割布局 -->
    <NSplit v-else direction="vertical" :default-size="splitSize" :min="0.3" :max="0.8" :resize-trigger-size="0.8"
      @update:size="handleSplitSizeChange">
      <template #1>
        <!-- 上半部分：聊天头部 + 消息列表 -->
        <div class="messages-section">
          <!-- 聊天头部 -->
          <div class="chat-header" @click="showThemeInfo = true">
            <div class="theme-info">
              <h2 class="theme-title">{{ currentTheme.title }}</h2>
              <p v-if="currentTheme.description" class="theme-description">
                <NEllipsis :line-clamp="1" style="max-width: 650px">
                  {{ currentTheme.description }}
                </NEllipsis>
              </p>
            </div>
            <div class="header-actions">
              <NButton quaternary circle @click="showThemeInfo = true">
                <template #icon>
                  <NIcon>
                    <InformationCircleOutline />
                  </NIcon>
                </template>
              </NButton>
            </div>
          </div>

          <!-- 消息列表 -->
          <div class="messages-container">
            <NScrollbar ref="scrollbarRef" style="height: 100%" @scroll="handleScroll">
              <div class="messages-list">
                <!-- 加载更多指示器 -->
                <div v-if="isLoadingMore" class="loading-more-indicator">
                  <NSpin size="small" />
                  <span>加载更多消息...</span>
                </div>

                <div v-for="message in currentMessages" :key="message.id" class="message-item">
                  <div class="message-content">
                    <div class="message-bubble">
                      <div class="message-text">
                        <MessageRenderer :message="message" />
                      </div>
                      <div class="message-meta">
                        <span class="message-time">
                          {{ formatMessageTime(message.createdAt) }}
                        </span>
                        <span v-if="message.isEdited" class="edited-indicator">已编辑</span>
                      </div>
                    </div>
                  </div>
                  <div class="message-actions">
                    <NDropdown :options="getMessageMenuOptions(message)" @select="handleMessageAction" trigger="hover">
                      <NButton quaternary circle size="small">
                        <template #icon>
                          <NIcon size="14">
                            <EllipsisHorizontalOutline />
                          </NIcon>
                        </template>
                      </NButton>
                    </NDropdown>
                  </div>
                </div>
              </div>
            </NScrollbar>
          </div>
        </div>
      </template>

      <template #2>
        <!-- 下半部分：输入区域 -->
        <div class="input-section">
          <MessageInput ref="messageInputRef" @message-sent="handleMessageSent" />
        </div>
      </template>
    </NSplit>

    <!-- 主题信息对话框 -->
    <NModal v-model:show="showThemeInfo" preset="card" style="width: 500px" title="主题信息">
      <div v-if="currentTheme" class="theme-info-panel">
        <div class="info-section">
          <h4>基本信息</h4>
          <div class="info-item">
            <span class="label">标题:</span>
            <span class="value">{{ currentTheme.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">描述:</span>
            <span class="value">{{ currentTheme.description || '无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(currentTheme.createdAt) }}</span>
          </div>
          <div class="info-item">
            <span class="label">消息数量:</span>
            <span class="value">{{ currentTheme.messageCount }}</span>
          </div>
        </div>

        <div v-if="currentTheme.tags.length > 0" class="info-section">
          <h4>标签</h4>
          <div class="tags-list">
            <NTag v-for="tag in currentTheme.tags" :key="tag" type="info">
              {{ tag }}
            </NTag>
          </div>
        </div>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import {
  ChatbubbleEllipsesOutline,
  CopyOutline,
  CreateOutline,
  EllipsisHorizontalOutline,
  InformationCircleOutline,
  TrashOutline
} from '@vicons/ionicons5'
import {
  NButton,
  NDropdown,
  NEllipsis,
  NIcon,
  NModal,
  NScrollbar,
  NSpin,
  NSplit,
  NTag,
  useMessage
} from 'naive-ui'
import { computed, h, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { useNoteMessagesStore } from '../stores/noteMessages'
import { useNoteThemesStore } from '../stores/noteThemes'
import type { NoteMessage } from '../types'
import MessageInput from './MessageInput.vue'
import MessageRenderer from './MessageRenderer.vue'

const message = useMessage()
const themesStore = useNoteThemesStore()
const messagesStore = useNoteMessagesStore()

// 引用
const scrollbarRef = ref()
const messageInputRef = ref<InstanceType<typeof MessageInput>>()

// 状态
const showThemeInfo = ref(false)
const splitSize = ref(0.7) // 默认70%给消息区域，30%给输入区域
const isRestoringScroll = ref(false) // 标记是否正在恢复滚动位置

// 计算属性
const currentTheme = computed(() => themesStore.currentTheme)
const currentMessages = computed(() => {
  if (!currentTheme.value) return []
  return messagesStore.getMessagesByTheme(currentTheme.value.id)
})

// 分页加载相关计算属性
const currentPaginationState = computed(() => {
  if (!currentTheme.value) return null
  return messagesStore.getThemePaginationState(currentTheme.value.id)
})

const isLoadingMore = computed(() => {
  return currentPaginationState.value?.isLoadingMore || false
})

const hasMoreMessages = computed(() => {
  return currentPaginationState.value?.hasMore || false
})

// 方法
const handleMessageSent = async () => {
  // 滚动到底部
  await nextTick()
  scrollToBottom()
}

const handleSplitSizeChange = (size: number) => {
  splitSize.value = size
  // 保存到本地存储
  localStorage.setItem('chatSplitSize', size.toString())
}

const scrollToBottom = () => {
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTo({ top: 999999, behavior: 'smooth' })
  }
}

// 恢复滚动位置
const restoreScrollPosition = (scrollPosition: number) => {
  if (scrollbarRef.value && scrollPosition > 0) {
    isRestoringScroll.value = true
    scrollbarRef.value.scrollTo({ top: scrollPosition, behavior: 'auto' })
    console.log('📍 恢复滚动位置:', scrollPosition)

    // 短暂延迟后重置标记
    setTimeout(() => {
      isRestoringScroll.value = false
    }, 100)
  }
}

// 保存当前滚动位置
const saveCurrentScrollPosition = () => {
  if (currentTheme.value && scrollbarRef.value) {
    const scrollbar = scrollbarRef.value.$el || scrollbarRef.value
    const scrollTop = scrollbar.scrollTop || 0
    messagesStore.updateTopicScrollPosition(currentTheme.value.id, scrollTop)
    console.log('💾 保存滚动位置:', { themeId: currentTheme.value.id, scrollTop })
  }
}

const formatMessageTime = (date: Date) => {
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()

  if (isToday) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

const formatDate = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

const getMessageMenuOptions = (msg: NoteMessage) => [
  {
    label: '复制',
    key: 'copy',
    icon: () => h(NIcon, null, { default: () => h(CopyOutline) }),
    message: msg
  },
  {
    label: '编辑',
    key: 'edit',
    icon: () => h(NIcon, null, { default: () => h(CreateOutline) }),
    message: msg
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => h(NIcon, null, { default: () => h(TrashOutline) }),
    message: msg
  }
]

const handleMessageAction = async (key: string, option: any) => {
  const msg = option.message

  switch (key) {
    case 'copy':
      navigator.clipboard.writeText(msg.content)
      message.success('已复制到剪贴板')
      break
    case 'edit':
      // TODO: 实现编辑功能
      message.info('编辑功能开发中')
      break
    case 'delete':
      try {
        await messagesStore.deleteMessage(msg.id)
        message.success('消息已删除')
      } catch (error) {
        console.error('删除消息失败:', error)
        message.error('删除消息失败')
      }
      break
  }
}



const handleScroll = async (e: Event) => {
  if (!currentTheme.value || !hasMoreMessages.value || isLoadingMore.value || isRestoringScroll.value) {
    return
  }

  const scrollbar = e.target as HTMLElement
  const scrollTop = scrollbar.scrollTop
  const scrollHeight = scrollbar.scrollHeight

  // 保存当前滚动位置到缓存
  messagesStore.updateTopicScrollPosition(currentTheme.value.id, scrollTop)

  // 当滚动到顶部附近时（距离顶部小于100px），加载更多消息
  if (scrollTop < 100) {
    console.log('🔄 检测到滚动到顶部，开始加载更多消息')

    // 记录当前滚动位置，用于加载完成后恢复位置
    const currentScrollHeight = scrollHeight

    const success = await messagesStore.loadMoreMessages(currentTheme.value.id)

    if (success) {
      // 加载成功后，调整滚动位置以保持用户的阅读位置
      await nextTick()
      if (scrollbarRef.value) {
        // 修复：直接使用 scrollbar 元素获取新的高度
        const scrollbarEl = scrollbarRef.value.$el || scrollbarRef.value
        const contentEl = scrollbarEl.querySelector ?
          scrollbarEl.querySelector('.n-scrollbar-content') :
          scrollbar
        const newScrollHeight = contentEl ? contentEl.scrollHeight : scrollbar.scrollHeight

        const heightDiff = newScrollHeight - currentScrollHeight
        scrollbarRef.value.scrollTo({ top: scrollTop + heightDiff, behavior: 'auto' })
      }
    }
  }
}

// 监听当前主题变化，智能加载消息和恢复状态
watch(currentTheme, async (newTheme, oldTheme) => {
  if (newTheme && newTheme.id !== oldTheme?.id) {
    console.log('🔄 主题切换:', { from: oldTheme?.id, to: newTheme.id })

    // 保存旧主题的滚动位置
    if (oldTheme) {
      saveCurrentScrollPosition()
    }

    // 获取新主题的缓存
    const cache = messagesStore.getTopicCache(newTheme.id)

    if (cache.isLoaded && cache.messages.length > 0) {
      // 如果缓存已存在，直接使用缓存并恢复滚动位置
      console.log('📋 使用缓存数据:', { themeId: newTheme.id, messageCount: cache.messages.length })

      await nextTick()

      if (cache.scrollPosition > 0) {
        // 恢复之前的滚动位置
        restoreScrollPosition(cache.scrollPosition)
      } else {
        // 如果没有保存的滚动位置，滚动到底部
        scrollToBottom()
      }
    } else {
      // 如果没有缓存，从服务器加载
      console.log('🌐 从服务器加载新主题消息:', newTheme.id)
      const success = await messagesStore.loadFromServer(newTheme.id)

      await nextTick()

      if (success) {
        // 新加载的消息，滚动到底部
        scrollToBottom()
      }
    }
  }
}, { immediate: true })

// 监听消息变化，智能滚动处理
watch(currentMessages, async (newMessages, oldMessages) => {
  if (!currentTheme.value) return

  await nextTick()

  // 只有在消息数量增加时才滚动到底部（新消息）
  // 避免在切换topic或加载历史消息时自动滚动
  if (newMessages.length > (oldMessages?.length || 0)) {
    const cache = messagesStore.getTopicCache(currentTheme.value.id)

    // 如果用户不在底部附近，不要自动滚动
    if (scrollbarRef.value) {
      const scrollbar = scrollbarRef.value.$el || scrollbarRef.value
      const scrollTop = scrollbar.scrollTop || 0
      const scrollHeight = scrollbar.scrollHeight || 0
      const clientHeight = scrollbar.clientHeight || 0

      // 如果用户在底部附近（距离底部小于100px），才自动滚动
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100

      if (isNearBottom || cache.scrollPosition === 0) {
        scrollToBottom()
      }
    } else {
      scrollToBottom()
    }
  }
})

// 组件挂载时恢复保存的分割大小
onMounted(() => {
  const savedSize = localStorage.getItem('chatSplitSize')
  if (savedSize) {
    const size = parseFloat(savedSize)
    if (size >= 0.3 && size <= 0.8) {
      splitSize.value = size
    }
  }
})

// 组件卸载前保存当前滚动位置
onBeforeUnmount(() => {
  saveCurrentScrollPosition()
})
</script>

<style scoped>
.chat-interface {
  height: 100vh;
  background-color: var(--n-color);
}

/* 消息区域样式 */
.messages-section {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--n-color);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: var(--n-color);
  border-bottom: 1px solid var(--n-border-color);
  flex-shrink: 0;
}

.theme-info {
  flex: 1;
}

.theme-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.theme-description {
  margin: 0;
  font-size: 14px;
  color: var(--n-text-color-2);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--n-color);
}

.empty-content {
  text-align: center;
  max-width: 300px;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: var(--n-text-color-1);
}

.empty-content p {
  margin: 0;
  color: var(--n-text-color-2);
  line-height: 1.5;
}

.messages-container {
  flex: 1;
  overflow: hidden;
  background-color: var(--n-color);
  height: calc(100% - 80px);
  /* 减去头部高度 */
}

.messages-list {
  padding: 16px 20px;
  min-height: 100%;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 16px;
}

.message-content {
  flex: 1;
}

.message-bubble {
  background-color: #18a058;
  color: white;
  padding: 12px 16px;
  border-radius: 18px 18px 4px 18px;
  max-width: 70%;
  margin-left: auto;
  word-wrap: break-word;
}

.message-text {
  line-height: 1.4;
  white-space: pre-wrap;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.message-time {
  font-size: 12px;
}

.edited-indicator {
  font-size: 11px;
  font-style: italic;
}

.message-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-item:hover .message-actions {
  opacity: 1;
}

/* 输入区域样式 */
.input-section {
  height: 100%;
  background-color: var(--n-color);
  display: flex;
  flex-direction: column;
}

/* Split 组件自定义样式 */
:deep(.n-split) {
  height: 100vh;
}

:deep(.n-split-pane) {
  height: 100%;
}

/* 自定义分隔条样式 */
:deep(.n-split__resize-trigger) {
  background-color: var(--n-border-color);
  transition: background-color 0.2s ease;
}

:deep(.n-split__resize-trigger:hover) {
  background-color: var(--n-primary-color);
  opacity: 0.8;
}



.theme-info-panel {
  padding: 16px 0;
}

.info-section {
  margin-bottom: 24px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-item .label {
  color: var(--n-text-color-2);
  font-size: 14px;
}

.info-item .value {
  color: var(--n-text-color-1);
  font-size: 14px;
  font-weight: 500;
}

.tags-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 加载更多指示器样式 */
.loading-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: var(--n-text-color-2);
  font-size: 14px;
  border-bottom: 1px solid var(--n-border-color);
  margin-bottom: 16px;
}
</style>
